# Teams Bot Webhook

Dự án Spring Boot 3.5.3 để nhận message từ Azure Bot Framework (Teams Bot) và in ra màn hình.

## Tính năng

- Nhận webhook từ Teams Bot
- In message ra console với thông tin chi tiết
- Log message vào file
- Health check endpoints
- Hỗ trợ ngrok để public URL

## Cấu trúc dự án

```
src/
├── main/
│   ├── java/com/example/teamswebhook/
│   │   ├── TeamsWebhookApplication.java    # Main application
│   │   ├── bot/
│   │   │   └── TeamsBot.java               # Bot logic
│   │   ├── config/
│   │   │   └── BotConfig.java              # Bot configuration
│   │   └── controller/
│   │       ├── BotController.java          # Bot webhook endpoint
│   │       └── HealthController.java       # Health check endpoints
│   └── resources/
│       └── application.yml                 # Configuration
```

## Cài đặt và chạy

### 1. Build project
```bash
mvn clean install
```

### 2. Chạy ứng dụng
```bash
mvn spring-boot:run
```

Hoặc:
```bash
java -jar target/teams-webhook-0.0.1-SNAPSHOT.jar
```

### 3. Sử dụng ngrok để public URL
```bash
# Cài đặt ngrok (nếu chưa có)
# Download từ https://ngrok.com/

# Public port 3978
ngrok http 3978
```

## Endpoints

- `GET /` - Trang chủ với thông tin service
- `GET /status` - Status check
- `POST /api/messages` - Webhook endpoint cho Teams Bot
- `GET /actuator/health` - Health check

## Cấu hình Bot

Để kết nối với Azure Bot Framework, cần cấu hình:

1. **Environment variables:**
   ```bash
   export BOT_APP_ID=your-bot-app-id
   export BOT_APP_PASSWORD=your-bot-app-password
   ```

2. **Hoặc trong application.yml:**
   ```yaml
   bot:
     app-id: your-bot-app-id
     app-password: your-bot-app-password
   ```

## Logs

- Console: Hiển thị message real-time
- File: `logs/teams-bot.log`

## Ví dụ output khi nhận message

```
=== TEAMS MESSAGE RECEIVED ===
User: John Doe (ID: 29:**********)
Message: Hello Bot!
Timestamp: 2024-01-15T10:30:45
Channel: msteams
Conversation ID: 19:meeting_abc123
===============================
```

## Testing

Sau khi setup ngrok và cấu hình bot, bạn có thể test bằng cách:

1. Gửi message trong Teams
2. Xem output trong console
3. Kiểm tra log file
4. Truy cập health endpoints

## Dependencies chính

- Spring Boot 3.5.3
- Microsoft Bot Framework SDK 4.14.1
- Jackson for JSON processing
- Spring Boot Actuator for monitoring
