package com.example.teamswebhook.controller;

import com.microsoft.bot.schema.Activity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

@RestController
public class TeamsBotController {

    private static final Logger logger = LoggerFactory.getLogger(TeamsBotController.class);

    @PostMapping("/api/messages")
    public ResponseEntity<Object> messages(@RequestBody Activity activity,
                                         @RequestHeader(value = "Authorization", required = false) String authHeader) {
        try {
            // In thông tin message ra console
            System.out.println("=== TEAMS MESSAGE RECEIVED ===");
            System.out.println("Activity Type: " + activity.getType());
            System.out.println("Channel ID: " + activity.getChannelId());
            System.out.println("Timestamp: " + LocalDateTime.now());

            if (activity.getFrom() != null) {
                System.out.println("From: " + activity.getFrom().getName() + " (ID: " + activity.getFrom().getId() + ")");
            }

            if (activity.getText() != null && !activity.getText().isEmpty()) {
                System.out.println("Message: " + activity.getText());
            }

            if (activity.getConversation() != null) {
                System.out.println("Conversation ID: " + activity.getConversation().getId());
            }

            System.out.println("Raw Activity: " + activity.toString());
            System.out.println("===============================");

            // Log vào file
            logger.info("Teams activity received - Type: {}, From: {}, Text: {}",
                       activity.getType(),
                       activity.getFrom() != null ? activity.getFrom().getName() : "Unknown",
                       activity.getText());

            return ResponseEntity.ok().build();

        } catch (Exception e) {
            System.err.println("Error processing Teams message: " + e.getMessage());
            logger.error("Error processing Teams message", e);
            return ResponseEntity.status(500).build();
        }
    }
}
