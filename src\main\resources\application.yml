server:
  port: 3978

spring:
  application:
    name: teams-webhook
  main:
    allow-bean-definition-overriding: true

# Bot Framework configuration
bot:
  app-id: ${BOT_APP_ID:}
  app-password: ${BOT_APP_PASSWORD:}

# Logging configuration
logging:
  level:
    com.example.teamswebhook: INFO
    com.microsoft.bot: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/teams-bot.log

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
