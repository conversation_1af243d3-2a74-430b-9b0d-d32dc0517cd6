package com.example.teamswebhook.config;

import com.example.teamswebhook.bot.TeamsBot;
import com.microsoft.bot.builder.Bot;
import com.microsoft.bot.integration.AdapterWithErrorHandler;
import com.microsoft.bot.integration.BotFrameworkHttpAdapter;
import com.microsoft.bot.integration.Configuration;
import com.microsoft.bot.integration.spring.BotController;
import com.microsoft.bot.integration.spring.BotDependencyConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@org.springframework.context.annotation.Configuration
@ConfigurationProperties("bot")
@Import({BotDependencyConfiguration.class})
public class BotConfig {
    
    private String appId;
    private String appPassword;
    
    @Bean
    public Bot getBot() {
        return new TeamsBot();
    }
    
    @Bean
    public BotController getBotController(AdapterWithErrorHandler adapter) {
        return new BotController(adapter);
    }
    
    // Getters and setters
    public String getAppId() {
        return appId;
    }
    
    public void setAppId(String appId) {
        this.appId = appId;
    }
    
    public String getAppPassword() {
        return appPassword;
    }
    
    public void setAppPassword(String appPassword) {
        this.appPassword = appPassword;
    }
}
