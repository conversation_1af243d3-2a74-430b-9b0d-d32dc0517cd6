@echo off
echo Starting Teams Bot Webhook...
echo.

REM Build the project
echo Building project...
call mvn clean install -q

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo.

REM Start the application
echo Starting Spring Boot application on port 3978...
echo.
echo Endpoints:
echo - Home: http://localhost:3978/
echo - Status: http://localhost:3978/status
echo - Webhook: http://localhost:3978/api/messages
echo - Health: http://localhost:3978/actuator/health
echo.
echo To use with ngrok, run in another terminal:
echo ngrok http 3978
echo.

call mvn spring-boot:run
