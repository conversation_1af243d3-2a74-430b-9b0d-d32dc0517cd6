package com.example.teamswebhook.bot;

import com.microsoft.bot.builder.ActivityHandler;
import com.microsoft.bot.builder.MessageFactory;
import com.microsoft.bot.builder.TurnContext;
import com.microsoft.bot.schema.Activity;
import com.microsoft.bot.schema.ChannelAccount;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@Component
public class TeamsBot extends ActivityHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(TeamsBot.class);

    @Override
    protected CompletableFuture<Void> onMessageActivity(TurnContext turnContext) {
        String messageText = turnContext.getActivity().getText();
        String userName = turnContext.getActivity().getFrom().getName();
        String userId = turnContext.getActivity().getFrom().getId();
        
        // In ra màn hình console
        System.out.println("=== TEAMS MESSAGE RECEIVED ===");
        System.out.println("User: " + userName + " (ID: " + userId + ")");
        System.out.println("Message: " + messageText);
        System.out.println("Timestamp: " + java.time.LocalDateTime.now());
        System.out.println("Channel: " + turnContext.getActivity().getChannelId());
        System.out.println("Conversation ID: " + turnContext.getActivity().getConversation().getId());
        System.out.println("===============================");
        
        // Log vào file
        logger.info("Teams message from {}: {}", userName, messageText);
        
        // Phản hồi lại user (tùy chọn)
        String replyText = "Đã nhận được tin nhắn: \"" + messageText + "\"";
        return turnContext.sendActivity(MessageFactory.text(replyText))
                .thenApply(result -> null);
    }

    @Override
    protected CompletableFuture<Void> onMembersAdded(List<ChannelAccount> membersAdded, TurnContext turnContext) {
        String welcomeText = "Chào mừng bạn đến với Teams Bot!";
        
        for (ChannelAccount member : membersAdded) {
            if (!member.getId().equals(turnContext.getActivity().getRecipient().getId())) {
                System.out.println("=== NEW MEMBER ADDED ===");
                System.out.println("Member: " + member.getName() + " (ID: " + member.getId() + ")");
                System.out.println("========================");
                
                logger.info("New member added: {}", member.getName());
                
                turnContext.sendActivity(MessageFactory.text(welcomeText));
            }
        }
        
        return CompletableFuture.completedFuture(null);
    }
    
    @Override
    protected CompletableFuture<Void> onInstallationUpdate(TurnContext turnContext) {
        System.out.println("=== BOT INSTALLATION UPDATE ===");
        System.out.println("Action: " + turnContext.getActivity().getAction());
        System.out.println("===============================");
        
        logger.info("Bot installation update: {}", turnContext.getActivity().getAction());
        
        return CompletableFuture.completedFuture(null);
    }
}
